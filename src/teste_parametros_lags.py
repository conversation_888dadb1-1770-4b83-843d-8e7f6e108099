#!/usr/bin/env python3
"""
Script de teste para verificar os parâmetros de lags separados
"""

import yaml

def carregar_configuracao():
    """Carrega configurações do arquivo config.yaml"""
    with open('config.yaml', 'r', encoding='utf-8') as file:
        return yaml.safe_load(file)

def main():
    """Função principal de teste"""
    print("🧪 TESTE DOS PARÂMETROS DE LAGS")
    print("=" * 50)
    
    # Carregar configuração
    config = carregar_configuracao()
    
    # Obter parâmetros
    ohlc_lags = config['xgboost']['features']['ohlc_lags']
    other_features_lags = config['xgboost']['features']['other_features_lags']
    
    print(f"📊 Parâmetros carregados:")
    print(f"   • OHLC lags: {ohlc_lags}")
    print(f"   • Other features lags: {other_features_lags}")
    print()
    
    # Simular criação de features
    print("🔧 Features que seriam criadas:")
    print()
    
    print("📈 OHLC pct_change features:")
    for i in range(1, ohlc_lags + 1):
        print(f"   • Media_OHLC_PctChange_Lag_{i}")
    print()
    
    print("📊 Volume features:")
    for i in range(1, other_features_lags + 1):
        print(f"   • Volume_Lag_{i}")
    print()
    
    print("💹 Spread features:")
    for i in range(1, other_features_lags + 1):
        print(f"   • Spread_Lag_{i}")
    print()
    
    print("📈 Volatilidade features:")
    for i in range(1, other_features_lags + 1):
        print(f"   • Volatilidade_Lag_{i}")
    print()
    
    print("🔬 Features econométricas (exemplo com Parkinson_Volatility):")
    for i in range(1, other_features_lags + 1):
        print(f"   • Parkinson_Volatility_Lag_{i}")
    print()
    
    # Calcular total de features
    total_features = ohlc_lags + (3 * other_features_lags) + (11 * other_features_lags)
    print(f"📊 RESUMO:")
    print(f"   • OHLC features: {ohlc_lags}")
    print(f"   • Basic features (Volume, Spread, Volatilidade): {3 * other_features_lags}")
    print(f"   • Econometric features (11 tipos): {11 * other_features_lags}")
    print(f"   • TOTAL DE FEATURES: {total_features}")
    print()
    
    print("✅ Teste concluído!")

if __name__ == "__main__":
    main()
