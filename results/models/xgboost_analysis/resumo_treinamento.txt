RESUMO DO TREINAMENTO XGBOOST BINÁRIO - SINAIS DE TRADING
======================================================================

Data de treinamento: 2025-07-09 14:56:35

MODELO BINÁRIO:
  • Tipo: XGBoost Binário (sem classe 'Sem Ação')
  • Classes: 0=Venda, 1=Compra
  • Função de perda: Binary cross-entropy (logloss)
  • Threshold de probabilidade: 0.55 (sinais só gerados se prob > 0.55)
  • Features básicas: pct_change da média OHLC (variação percentual), Volume, Spread, Volatilidade
  • Features econométricas: Parkinson, Garman-Klass, MFI, EMV, Amihud, Roll Spread,
    Hurst, Vol/Volume, CMF, A/D Line, Volume Oscillator (12 features)
  • Total de features: 35
  • Acurácia geral: 0.603

CONFIGURAÇÕES UTILIZADAS:
  • Período de dados: 10y
  • Horizonte de sinais: 1 dias
  • Lags OHLC: 20
  • Janela volatilidade: 20
  • Multiplicador spread: 0.5

FEATURES UTILIZADAS (35):
   1. Media_OHLC_PctChange_Lag_1
   2. Media_OHLC_PctChange_Lag_2
   3. Media_OHLC_PctChange_Lag_3
   4. Media_OHLC_PctChange_Lag_4
   5. Media_OHLC_PctChange_Lag_5
   6. Media_OHLC_PctChange_Lag_6
   7. Media_OHLC_PctChange_Lag_7
   8. Media_OHLC_PctChange_Lag_8
   9. Media_OHLC_PctChange_Lag_9
  10. Media_OHLC_PctChange_Lag_10
  11. Media_OHLC_PctChange_Lag_11
  12. Media_OHLC_PctChange_Lag_12
  13. Media_OHLC_PctChange_Lag_13
  14. Media_OHLC_PctChange_Lag_14
  15. Media_OHLC_PctChange_Lag_15
  16. Media_OHLC_PctChange_Lag_16
  17. Media_OHLC_PctChange_Lag_17
  18. Media_OHLC_PctChange_Lag_18
  19. Media_OHLC_PctChange_Lag_19
  20. Media_OHLC_PctChange_Lag_20
  21. Volume
  22. Spread
  23. Volatilidade
  24. Parkinson_Volatility
  25. GK_Volatility
  26. MFI
  27. EMV
  28. EMV_MA
  29. Amihud
  30. Roll_Spread
  31. Hurst
  32. Vol_per_Volume
  33. CMF
  34. AD_Line
  35. VO

RESULTADOS DO MODELO:
  • Acurácia Geral: 0.603
  • Distribuição das Predições:
    - Venda: 4300 (46.9%)
    - Compra: 4875 (53.1%)

DEFINIÇÃO DOS SINAIS:
  • Sinal de Compra: Média OHLC atual < Média OHLC 1 dias à frente
  • Sinal de Venda: Média OHLC atual > Média OHLC 1 dias à frente
  • Sem Ação: Casos onde não há sinal de compra nem venda

PARÂMETROS DO XGBOOST:
  • n_estimators: 100
  • max_depth: 6
  • learning_rate: 0.1
  • random_state: 42
  • eval_metric: logloss
  • objective: multi:softprob (adicionado automaticamente)
  • num_class: 3 (adicionado automaticamente)
  • eval_metric: mlogloss (adicionado automaticamente)
