RESUMO DO TREINAMENTO XGBOOST BINÁRIO - SINAIS DE TRADING
======================================================================

Data de treinamento: 2025-07-09 15:16:32

MODELO BINÁRIO:
  • Tipo: XGBoost Binário (sem classe 'Sem Ação')
  • Classes: 0=Venda, 1=Compra
  • Função de perda: Binary cross-entropy (logloss)
  • Threshold de probabilidade: 0.55 (sinais só gerados se prob > 0.55)
  • Features básicas: pct_change da média OHLC (variação percentual), Volume, Spread, Volatilidade
  • Features econométricas: Parkinson, Garman-Klass, MFI, EMV, Amihud, Roll Spread,
    Hurst, Vol/Volume, CMF, A/D Line, Volume Oscillator (12 features)
  • Total de features: 150
  • Acurácia geral: 0.507

CONFIGURAÇÕES UTILIZADAS:
  • Período de dados: 10y
  • Horizonte de sinais: 1 dias
  • Lags OHLC: 10
  • Janela volatilidade: 20
  • Multiplicador spread: 0.5

FEATURES UTILIZADAS (150):
   1. Media_OHLC_PctChange_Lag_1
   2. Media_OHLC_PctChange_Lag_2
   3. Media_OHLC_PctChange_Lag_3
   4. Media_OHLC_PctChange_Lag_4
   5. Media_OHLC_PctChange_Lag_5
   6. Media_OHLC_PctChange_Lag_6
   7. Media_OHLC_PctChange_Lag_7
   8. Media_OHLC_PctChange_Lag_8
   9. Media_OHLC_PctChange_Lag_9
  10. Media_OHLC_PctChange_Lag_10
  11. Volume_Lag_1
  12. Volume_Lag_2
  13. Volume_Lag_3
  14. Volume_Lag_4
  15. Volume_Lag_5
  16. Volume_Lag_6
  17. Volume_Lag_7
  18. Volume_Lag_8
  19. Volume_Lag_9
  20. Volume_Lag_10
  21. Spread_Lag_1
  22. Spread_Lag_2
  23. Spread_Lag_3
  24. Spread_Lag_4
  25. Spread_Lag_5
  26. Spread_Lag_6
  27. Spread_Lag_7
  28. Spread_Lag_8
  29. Spread_Lag_9
  30. Spread_Lag_10
  31. Volatilidade_Lag_1
  32. Volatilidade_Lag_2
  33. Volatilidade_Lag_3
  34. Volatilidade_Lag_4
  35. Volatilidade_Lag_5
  36. Volatilidade_Lag_6
  37. Volatilidade_Lag_7
  38. Volatilidade_Lag_8
  39. Volatilidade_Lag_9
  40. Volatilidade_Lag_10
  41. Parkinson_Volatility_Lag_1
  42. Parkinson_Volatility_Lag_2
  43. Parkinson_Volatility_Lag_3
  44. Parkinson_Volatility_Lag_4
  45. Parkinson_Volatility_Lag_5
  46. Parkinson_Volatility_Lag_6
  47. Parkinson_Volatility_Lag_7
  48. Parkinson_Volatility_Lag_8
  49. Parkinson_Volatility_Lag_9
  50. Parkinson_Volatility_Lag_10
  51. MFI_Lag_1
  52. MFI_Lag_2
  53. MFI_Lag_3
  54. MFI_Lag_4
  55. MFI_Lag_5
  56. MFI_Lag_6
  57. MFI_Lag_7
  58. MFI_Lag_8
  59. MFI_Lag_9
  60. MFI_Lag_10
  61. EMV_Lag_1
  62. EMV_Lag_2
  63. EMV_Lag_3
  64. EMV_Lag_4
  65. EMV_Lag_5
  66. EMV_Lag_6
  67. EMV_Lag_7
  68. EMV_Lag_8
  69. EMV_Lag_9
  70. EMV_Lag_10
  71. EMV_MA_Lag_1
  72. EMV_MA_Lag_2
  73. EMV_MA_Lag_3
  74. EMV_MA_Lag_4
  75. EMV_MA_Lag_5
  76. EMV_MA_Lag_6
  77. EMV_MA_Lag_7
  78. EMV_MA_Lag_8
  79. EMV_MA_Lag_9
  80. EMV_MA_Lag_10
  81. Amihud_Lag_1
  82. Amihud_Lag_2
  83. Amihud_Lag_3
  84. Amihud_Lag_4
  85. Amihud_Lag_5
  86. Amihud_Lag_6
  87. Amihud_Lag_7
  88. Amihud_Lag_8
  89. Amihud_Lag_9
  90. Amihud_Lag_10
  91. Roll_Spread_Lag_1
  92. Roll_Spread_Lag_2
  93. Roll_Spread_Lag_3
  94. Roll_Spread_Lag_4
  95. Roll_Spread_Lag_5
  96. Roll_Spread_Lag_6
  97. Roll_Spread_Lag_7
  98. Roll_Spread_Lag_8
  99. Roll_Spread_Lag_9
  100. Roll_Spread_Lag_10
  101. Hurst_Lag_1
  102. Hurst_Lag_2
  103. Hurst_Lag_3
  104. Hurst_Lag_4
  105. Hurst_Lag_5
  106. Hurst_Lag_6
  107. Hurst_Lag_7
  108. Hurst_Lag_8
  109. Hurst_Lag_9
  110. Hurst_Lag_10
  111. Vol_per_Volume_Lag_1
  112. Vol_per_Volume_Lag_2
  113. Vol_per_Volume_Lag_3
  114. Vol_per_Volume_Lag_4
  115. Vol_per_Volume_Lag_5
  116. Vol_per_Volume_Lag_6
  117. Vol_per_Volume_Lag_7
  118. Vol_per_Volume_Lag_8
  119. Vol_per_Volume_Lag_9
  120. Vol_per_Volume_Lag_10
  121. CMF_Lag_1
  122. CMF_Lag_2
  123. CMF_Lag_3
  124. CMF_Lag_4
  125. CMF_Lag_5
  126. CMF_Lag_6
  127. CMF_Lag_7
  128. CMF_Lag_8
  129. CMF_Lag_9
  130. CMF_Lag_10
  131. AD_Line_Lag_1
  132. AD_Line_Lag_2
  133. AD_Line_Lag_3
  134. AD_Line_Lag_4
  135. AD_Line_Lag_5
  136. AD_Line_Lag_6
  137. AD_Line_Lag_7
  138. AD_Line_Lag_8
  139. AD_Line_Lag_9
  140. AD_Line_Lag_10
  141. VO_Lag_1
  142. VO_Lag_2
  143. VO_Lag_3
  144. VO_Lag_4
  145. VO_Lag_5
  146. VO_Lag_6
  147. VO_Lag_7
  148. VO_Lag_8
  149. VO_Lag_9
  150. VO_Lag_10

RESULTADOS DO MODELO:
  • Acurácia Geral: 0.507
  • Distribuição das Predições:
    - Venda: 3819 (41.6%)
    - Compra: 5356 (58.4%)

DEFINIÇÃO DOS SINAIS:
  • Sinal de Compra: Média OHLC atual < Média OHLC 1 dias à frente
  • Sinal de Venda: Média OHLC atual > Média OHLC 1 dias à frente
  • Sem Ação: Casos onde não há sinal de compra nem venda

PARÂMETROS DO XGBOOST:
  • n_estimators: 100
  • max_depth: 6
  • learning_rate: 0.1
  • random_state: 42
  • eval_metric: logloss
  • objective: multi:softprob (adicionado automaticamente)
  • num_class: 3 (adicionado automaticamente)
  • eval_metric: mlogloss (adicionado automaticamente)
